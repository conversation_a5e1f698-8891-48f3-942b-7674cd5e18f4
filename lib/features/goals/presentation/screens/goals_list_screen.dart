import 'package:budapp/data/models/goal.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/goals/presentation/widgets/goal_progress_bar.dart';
import 'package:budapp/features/goals/providers/goal_providers.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/widgets/common/empty_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen displaying a list of user goals
class GoalsListScreen extends ConsumerWidget {
  const GoalsListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goalsAsync = ref.watch(activeUserGoalsProvider);

    return Scaffold(
      appBar: AppBarHelpers.createStandardScrollableAppBar(title: 'Goals'),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(activeUserGoalsProvider);
        },
        child: goalsAsync.when(
          data: (goals) => _buildGoalsList(context, goals),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text('Failed to load goals'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.invalidate(activeUserGoalsProvider),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/goals/create'),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildGoalsList(BuildContext context, List<Goal> goals) {
    if (goals.isEmpty) {
      return const Center(
        child: EmptyState(
          icon: Icons.flag_outlined,
          title: 'No Goals Yet',
          message:
              'Create your first financial goal to start tracking your progress.',
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: goals.length,
      itemBuilder: (context, index) {
        final goal = goals[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _GoalCard(goal: goal),
        );
      },
    );
  }
}

/// Individual goal card widget
class _GoalCard extends ConsumerWidget {
  const _GoalCard({required this.goal});

  final Goal goal;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      elevation: 0,
      color: goal.colorHex != null
          ? Color(
              int.parse('0xFF${goal.colorHex!.substring(1)}'),
            ).withValues(alpha: 0.1)
          : colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: InkWell(
        onTap: () => context.push('/goals/${goal.id}/edit'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with icon and menu
              Row(
                children: [
                  // Goal icon
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: goal.colorHex != null
                          ? Color(
                              int.parse('0xFF${goal.colorHex!.substring(1)}'),
                            )
                          : colorScheme.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getGoalIcon(goal.iconName),
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Goal name and status
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          goal.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        _StatusChip(status: goal.status),
                      ],
                    ),
                  ),

                  // Menu button
                  PopupMenuButton<String>(
                    onSelected: (value) =>
                        _handleMenuAction(context, ref, value),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'add_contribution',
                        child: ListTile(
                          leading: Icon(Icons.add_circle_outline),
                          title: Text('Add Contribution'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'view_contributions',
                        child: ListTile(
                          leading: Icon(Icons.list),
                          title: Text('View Contributions'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('Edit'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete),
                          title: Text('Delete'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Progress section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Amount row
                  Consumer(
                    builder: (context, ref, _) {
                      final currencyFormatter = ref.watch(
                        currencyFormatterProvider,
                      );
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            currencyFormatter.formatAmount(
                              goal.currentAmountCents,
                            ),
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.primary,
                            ),
                          ),
                          Text(
                            'of ${currencyFormatter.formatAmount(goal.targetAmountCents)}',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      );
                    },
                  ),

                  const SizedBox(height: 8),

                  // Enhanced progress indicator
                  GoalProgressBar(
                    goal: goal,
                    type: GoalProgressType.linear,
                    height: 8,
                    showPercentage: true,
                    animated: true,
                  ),
                ],
              ),

              // Description (if available)
              if (goal.description?.isNotEmpty ?? false) ...[
                const SizedBox(height: 12),
                Text(
                  goal.description!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              // Target date (if available)
              if (goal.targetDate != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 16,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Target: ${_formatDate(goal.targetDate!)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _handleMenuAction(BuildContext context, WidgetRef ref, String action) {
    switch (action) {
      case 'add_contribution':
        context.push('/goals/${goal.id}/contributions/create');
      case 'view_contributions':
        context.push('/goals/${goal.id}/contributions');
      case 'edit':
        context.push('/goals/${goal.id}/edit');
      case 'delete':
        _showDeleteConfirmation(context, ref);
    }
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Goal'),
        content: Text('Are you sure you want to delete "${goal.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref.read(goalDeletionProvider.notifier).deleteGoal(goal.id);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  IconData _getGoalIcon(String? iconName) {
    switch (iconName) {
      case 'savings':
        return Icons.savings;
      case 'account_balance':
        return Icons.account_balance;
      case 'trending_up':
        return Icons.trending_up;
      case 'home':
        return Icons.home;
      case 'directions_car':
        return Icons.directions_car;
      case 'flight':
        return Icons.flight;
      case 'school':
        return Icons.school;
      default:
        return Icons.flag;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Status chip widget
class _StatusChip extends StatelessWidget {
  const _StatusChip({required this.status});

  final GoalStatus status;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Color chipColor;
    String label;

    switch (status) {
      case GoalStatus.active:
        chipColor = colorScheme.primary;
        label = 'Active';
      case GoalStatus.paused:
        chipColor = colorScheme.tertiary;
        label = 'Paused';
      case GoalStatus.completed:
        chipColor = Colors.green;
        label = 'Completed';
      case GoalStatus.cancelled:
        chipColor = colorScheme.error;
        label = 'Cancelled';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: theme.textTheme.labelSmall?.copyWith(
          color: chipColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
