import 'package:budapp/config/environment_config.dart';
import 'package:budapp/features/auth/services/auth_error_service.dart';
import 'package:budapp/features/dashboard/presentation/screens/home_screen.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/widgets/splash_screen.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Authentication wrapper that shows the authenticated app content
///
/// This wrapper is simplified since go_router now handles authentication routing.
/// It primarily serves as a loading/error dispatcher and shows the main app
/// when the user reaches this point (router has already verified authentication).
class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // debugPrint('[AuthWrapper] build');
    // Watch authentication state using Riverpod providers
    final authState = ref.watch(authStateProvider);

    return authState.when(
      data: (user) {
        // If we reach this point, go_router has already verified:
        // 1. User is authenticated (user != null)
        // 2. User's email is verified (user.emailVerified == true)
        // So we can safely show the main app content
        return const HomeScreen();
      },
      loading: () => SplashScreen(
        environment: EnvironmentConfig.environmentName,
        themeColor: EnvironmentConfig.themeColor,
      ),
      error: (error, stackTrace) => _buildErrorScreen(context, ref, error),
    );
  }

  Widget _buildErrorScreen(BuildContext context, WidgetRef ref, Object error) {
    final theme = Theme.of(context);

    // Process error through AuthErrorService for user-friendly messages
    String errorMessage;
    if (error is FirebaseAuthException) {
      final authError = AuthErrorService.handleFirebaseAuthException(error);
      errorMessage = authError.userMessage;
    } else {
      // Fallback for non-Firebase errors
      errorMessage =
          'An unexpected authentication error occurred. Please try again.';
    }

    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 24),
              Text(
                'Authentication Error',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                errorMessage,
                textAlign: TextAlign.center,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  // Invalidate the auth state to retry
                  ref.invalidate(authStateProvider);
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
