import 'package:budapp/config/app_theme.dart';
import 'package:budapp/config/environment_config.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/routing/app_router.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase with environment-specific configuration
    // Automatically detects flavor (dev/staging/prod) and uses appropriate config
    await Firebase.initializeApp(options: EnvironmentConfig.firebaseOptions);

    debugPrint(EnvironmentConfig.firebaseInitMessage);

    // Initialize Firebase App Check for additional security
    await EnvironmentConfig.initializeAppCheck();
  } on Exception catch (e) {
    debugPrint('Error initializing Firebase: $e');
    // Continue with app launch even if Firebase fails
  }

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final initialization = ref.watch(initializationProvider);

    return initialization.when(
      loading: () => MaterialApp(
        title: EnvironmentConfig.appTitle,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        home: const Scaffold(body: Center(child: CircularProgressIndicator())),
      ),
      error: (error, stackTrace) => MaterialApp(
        title: EnvironmentConfig.appTitle,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        home: Scaffold(
          body: Center(child: Text('Error initializing app: $error')),
        ),
      ),
      data: (_) {
        final themeMode = ref.watch(themeModeProvider);
        ThemeMode selectedThemeMode;
        switch (themeMode) {
          case 'light':
            selectedThemeMode = ThemeMode.light;
          case 'dark':
            selectedThemeMode = ThemeMode.dark;
          case 'system':
          default:
            selectedThemeMode = ThemeMode.system;
        }

        final router = ref.watch(goRouterProvider);

        return MaterialApp.router(
          title: EnvironmentConfig.appTitle,
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: selectedThemeMode,
          routerConfig: router,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en'), // English
          ],
        );
      },
    );
  }
}
