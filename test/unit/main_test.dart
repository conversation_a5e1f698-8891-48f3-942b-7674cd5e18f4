import 'dart:async';

import 'package:budapp/config/app_theme.dart';
import 'package:budapp/config/environment_config.dart';
import 'package:budapp/main.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';

// Helper function to create a minimal GoRouter for testing
GoRouter createTestRouter() {
  return GoRouter(
    initialLocation: '/',
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) =>
            const Scaffold(body: Center(child: Text('Test Home'))),
      ),
    ],
  );
}

void main() {
  group('Main App Tests', () {
    group('MyApp Widget Tests', () {
      testWidgets(
        'should render loading state when initialization is loading',
        (tester) async {
          // Arrange - Create a provider that never completes (stays loading)
          final loadingCompleter = Completer<void>();

          await tester.pumpWidget(
            ProviderScope(
              overrides: [
                initializationProvider.overrideWith(
                  (ref) => loadingCompleter.future,
                ),
              ],
              child: const MyApp(),
            ),
          );

          // Act - Pump once to build the widget tree
          await tester.pump();

          // Assert
          expect(find.byType(CircularProgressIndicator), findsOneWidget);
          expect(find.text('Error initializing app:'), findsNothing);
          expect(find.byType(MaterialApp), findsOneWidget);
          expect(find.byType(Scaffold), findsOneWidget);

          // Verify app title and theme
          final materialApp = tester.widget<MaterialApp>(
            find.byType(MaterialApp),
          );
          expect(materialApp.title, equals(EnvironmentConfig.appTitle));
          expect(materialApp.theme, equals(AppTheme.lightTheme));
          expect(materialApp.darkTheme, equals(AppTheme.darkTheme));
        },
      );

      testWidgets('should render error state when initialization fails', (
        tester,
      ) async {
        // Arrange - Create a provider that throws an error
        const errorMessage = 'Test initialization error';

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              initializationProvider.overrideWith(
                (ref) => Future<void>.error(errorMessage),
              ),
            ],
            child: const MyApp(),
          ),
        );

        // Act - Pump to build and let the error propagate
        await tester.pump();
        await tester.pump(); // Second pump to handle async error

        // Assert
        expect(
          find.text('Error initializing app: $errorMessage'),
          findsOneWidget,
        );
        expect(find.byType(CircularProgressIndicator), findsNothing);
        expect(find.byType(MaterialApp), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);

        // Verify app configuration
        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.title, equals(EnvironmentConfig.appTitle));
        expect(materialApp.theme, equals(AppTheme.lightTheme));
        expect(materialApp.darkTheme, equals(AppTheme.darkTheme));
      });

      testWidgets(
        'should render successful initialization with MaterialApp.router',
        (tester) async {
          // Arrange - Create successful initialization and test router
          final testRouter = createTestRouter();

          await tester.pumpWidget(
            ProviderScope(
              overrides: [
                initializationProvider.overrideWith(
                  (ref) => Future<void>.value(),
                ),
                goRouterProvider.overrideWithValue(testRouter),
                themeModeProvider.overrideWith((ref) => 'system'),
              ],
              child: const MyApp(),
            ),
          );

          // Act - Pump to complete initialization
          await tester.pump();
          await tester.pump(); // Second pump for async completion

          // Assert
          expect(find.byType(CircularProgressIndicator), findsNothing);
          expect(find.text('Error initializing app:'), findsNothing);
          expect(find.byType(MaterialApp), findsOneWidget);

          // Verify MaterialApp.router is used for successful initialization
          final materialApp = tester.widget<MaterialApp>(
            find.byType(MaterialApp),
          );
          expect(materialApp.routerConfig, equals(testRouter));
          expect(materialApp.title, equals(EnvironmentConfig.appTitle));
          expect(materialApp.theme, equals(AppTheme.lightTheme));
          expect(materialApp.darkTheme, equals(AppTheme.darkTheme));
          expect(materialApp.themeMode, equals(ThemeMode.system));
        },
      );

      testWidgets('should handle light theme mode correctly', (tester) async {
        // Arrange
        final testRouter = createTestRouter();

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              initializationProvider.overrideWith(
                (ref) => Future<void>.value(),
              ),
              goRouterProvider.overrideWithValue(testRouter),
              themeModeProvider.overrideWith((ref) => 'light'),
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();
        await tester.pump();

        // Assert
        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.themeMode, equals(ThemeMode.light));
      });

      testWidgets('should handle dark theme mode correctly', (tester) async {
        // Arrange
        final testRouter = createTestRouter();

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              initializationProvider.overrideWith(
                (ref) => Future<void>.value(),
              ),
              goRouterProvider.overrideWithValue(testRouter),
              themeModeProvider.overrideWith((ref) => 'dark'),
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();
        await tester.pump();

        // Assert
        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.themeMode, equals(ThemeMode.dark));
      });

      testWidgets('should default to system theme for unknown values', (
        tester,
      ) async {
        // Arrange
        final testRouter = createTestRouter();

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              initializationProvider.overrideWith(
                (ref) => Future<void>.value(),
              ),
              goRouterProvider.overrideWithValue(testRouter),
              themeModeProvider.overrideWith((ref) => 'unknown_theme'),
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();
        await tester.pump();

        // Assert
        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.themeMode, equals(ThemeMode.system));
      });

      testWidgets('should configure localization delegates correctly', (
        tester,
      ) async {
        // Arrange
        final testRouter = createTestRouter();

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              initializationProvider.overrideWith(
                (ref) => Future<void>.value(),
              ),
              goRouterProvider.overrideWithValue(testRouter),
              themeModeProvider.overrideWith((ref) => 'system'),
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();
        await tester.pump();

        // Assert
        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.localizationsDelegates, isNotNull);
        expect(materialApp.localizationsDelegates!.length, equals(4));
        expect(materialApp.supportedLocales, contains(const Locale('en')));
      });
    });

    group('App Configuration Tests', () {
      test('should have correct environment configuration access', () {
        // Test that EnvironmentConfig properties are accessible
        expect(EnvironmentConfig.appTitle, isNotNull);
        expect(EnvironmentConfig.appTitle, isNotEmpty);
        expect(EnvironmentConfig.firebaseOptions, isNotNull);
        expect(EnvironmentConfig.firebaseInitMessage, isNotNull);
      });

      test('should have correct theme configuration', () {
        // Test that AppTheme properties are accessible
        expect(AppTheme.lightTheme, isNotNull);
        expect(AppTheme.darkTheme, isNotNull);
        expect(AppTheme.lightTheme, isA<ThemeData>());
        expect(AppTheme.darkTheme, isA<ThemeData>());
      });
    });

    group('Theme Mode Switch Logic Tests', () {
      test('should correctly map theme mode strings to ThemeMode enum', () {
        // Test the theme mode mapping logic that's used in the widget
        const testCases = {
          'light': ThemeMode.light,
          'dark': ThemeMode.dark,
          'system': ThemeMode.system,
          'unknown': ThemeMode.system, // default case
          '': ThemeMode.system, // empty string case
        };

        for (final entry in testCases.entries) {
          final input = entry.key;
          final expected = entry.value;

          // Simulate the switch logic from MyApp.build
          ThemeMode result;
          switch (input) {
            case 'light':
              result = ThemeMode.light;
            case 'dark':
              result = ThemeMode.dark;
            case 'system':
            default:
              result = ThemeMode.system;
          }

          expect(result, equals(expected), reason: 'Failed for input: $input');
        }
      });
    });

    group('Widget Structure Tests', () {
      testWidgets('should have correct widget hierarchy for loading state', (
        tester,
      ) async {
        // Arrange
        final loadingCompleter = Completer<void>();

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              initializationProvider.overrideWith(
                (ref) => loadingCompleter.future,
              ),
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();

        // Assert widget hierarchy
        expect(find.byType(ProviderScope), findsOneWidget);
        expect(find.byType(MyApp), findsOneWidget);
        expect(find.byType(MaterialApp), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(Center), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('should have correct widget hierarchy for error state', (
        tester,
      ) async {
        // Arrange
        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              initializationProvider.overrideWith(
                (ref) => Future<void>.error('Test error'),
              ),
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();
        await tester.pump();

        // Assert widget hierarchy
        expect(find.byType(ProviderScope), findsOneWidget);
        expect(find.byType(MyApp), findsOneWidget);
        expect(find.byType(MaterialApp), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(Center), findsOneWidget);
        expect(find.byType(Text), findsOneWidget);
        expect(find.textContaining('Error initializing app:'), findsOneWidget);
      });

      testWidgets('should have correct widget hierarchy for success state', (
        tester,
      ) async {
        // Arrange
        final testRouter = createTestRouter();

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              initializationProvider.overrideWith(
                (ref) => Future<void>.value(),
              ),
              goRouterProvider.overrideWithValue(testRouter),
              themeModeProvider.overrideWith((ref) => 'system'),
            ],
            child: const MyApp(),
          ),
        );

        // Act
        await tester.pump();
        await tester.pump();

        // Assert widget hierarchy
        expect(find.byType(ProviderScope), findsOneWidget);
        expect(find.byType(MyApp), findsOneWidget);
        expect(find.byType(MaterialApp), findsOneWidget);

        // Verify MaterialApp.router is used (no Scaffold in this case)
        final materialApp = tester.widget<MaterialApp>(
          find.byType(MaterialApp),
        );
        expect(materialApp.routerConfig, isNotNull);
      });
    });
  });
}
