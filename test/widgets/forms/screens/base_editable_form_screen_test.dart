import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/screens/base_editable_form_screen.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/test_isolation_helper.dart';
import '../../../helpers/test_wrapper.dart';

class FakeUser extends Fake implements User {}

// Factory functions for creating test instances
Account createTestAccount({
  String id = 'test-account-id',
  String userId = 'test-user-id',
  String name = 'Test Account',
}) {
  return Account(
    id: id,
    userId: userId,
    name: name,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

Transaction createTestTransaction({
  String id = 'test-transaction-id',
  String userId = 'test-user-id',
  int amountCents = 1000,
}) {
  return Transaction(
    id: id,
    userId: userId,
    amountCents: amountCents,
    transactionDate: DateTime.now(),
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

Category createTestCategory({
  String id = 'test-category-id',
  String userId = 'test-user-id',
  String name = 'Test Category',
}) {
  return Category(
    id: id,
    userId: userId,
    name: name,
    type: CategoryType.expense,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeUser());
    registerFallbackValue(createTestAccount());
    registerFallbackValue(createTestTransaction());
    registerFallbackValue(createTestCategory());
  });
  group('BaseEditableFormScreen Tests', () {
    late List<FormFieldConfig<dynamic>> testFieldConfigs;
    late Future<void> Function(Map<String, dynamic>) mockOnSubmit;
    late Future<void> Function()? mockOnDelete;
    late bool onSubmitCalled;
    late bool onDeleteCalled;
    late Map<String, dynamic> submittedData;

    setUp(() {
      // Setup test isolation
      TestIsolationHelper.setUp();

      onSubmitCalled = false;
      onDeleteCalled = false;
      submittedData = {};

      mockOnSubmit = (Map<String, dynamic> data) async {
        onSubmitCalled = true;
        submittedData = data;
      };

      mockOnDelete = () async {
        onDeleteCalled = true;
      };

      testFieldConfigs = [
        TextFieldConfig(
          key: 'name',
          label: 'Name',
          hintText: 'Enter name',
          isRequired: true,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Name is required';
            }
            return null;
          },
        ),
        const TextFieldConfig(
          key: 'description',
          label: 'Description',
          hintText: 'Enter description',
          isRequired: false,
        ),
      ];
    });

    tearDown(TestIsolationHelper.tearDown);

    group('Basic Form Rendering', () {
      testWidgets('should render form with basic field configurations', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Verify title is displayed
        expect(find.text('Test Form'), findsOneWidget);

        // Verify form fields are rendered
        expect(find.byType(TextFormField), findsAtLeastNWidgets(2));
        expect(
          find.text('Name *'),
          findsOneWidget,
        ); // Required fields have asterisk
        expect(find.text('Description'), findsOneWidget);

        // Verify submit button is rendered (in create mode, button says "Create")
        expect(find.text('Create'), findsOneWidget);
      });

      testWidgets('should render form with custom submit button text', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
              submitButtonText: 'Create Item',
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.text('Create Item'), findsOneWidget);
        expect(find.text('Create'), findsNothing);
      });

      testWidgets('should render delete button when showDeleteButton is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
              showDeleteButton: true,
              onDelete: mockOnDelete,
            ),
          ),
        );
        await tester.pumpAndSettle();

        expect(find.byIcon(Icons.delete_outline), findsOneWidget);
      });

      testWidgets(
        'should not render delete button when showDeleteButton is false',
        (tester) async {
          await tester.pumpWidget(
            TestWrapper.createTestWidgetWithRouter(
              BaseEditableFormScreen<String>(
                title: 'Test Form',
                fieldConfigs: testFieldConfigs,
                onSubmit: mockOnSubmit,
                showDeleteButton: false,
                onDelete: mockOnDelete,
              ),
            ),
          );
          await tester.pumpAndSettle();

          expect(find.byIcon(Icons.delete_outline), findsNothing);
        },
      );
    });

    group('Form Validation', () {
      testWidgets('should show validation errors for required fields', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Try to submit form without filling required fields
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Should show validation error for required field
        expect(find.text('Name is required'), findsOneWidget);
        expect(onSubmitCalled, isFalse);
      });

      testWidgets('should not show validation errors for optional fields', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Fill only the required field
        await tester.enterText(find.byType(TextFormField).first, 'Test Name');
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Should submit successfully without validation errors
        expect(onSubmitCalled, isTrue);
        expect(submittedData['name'], equals('Test Name'));
      });

      testWidgets('should validate form fields on submit', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Fill all fields
        await tester.enterText(find.byType(TextFormField).first, 'Test Name');
        await tester.enterText(
          find.byType(TextFormField).last,
          'Test Description',
        );
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Should submit successfully
        expect(onSubmitCalled, isTrue);
        expect(submittedData['name'], equals('Test Name'));
        expect(submittedData['description'], equals('Test Description'));
      });
    });

    group('Form Submission', () {
      testWidgets('should call onSubmit with form data when form is valid', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Fill form with valid data
        await tester.enterText(find.byType(TextFormField).first, 'Valid Name');
        await tester.enterText(
          find.byType(TextFormField).last,
          'Valid Description',
        );
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Verify onSubmit was called with correct data
        expect(onSubmitCalled, isTrue);
        expect(submittedData['name'], equals('Valid Name'));
        expect(submittedData['description'], equals('Valid Description'));
      });

      testWidgets('should not call onSubmit when form is invalid', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Try to submit form with invalid data (empty required field)
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Verify onSubmit was not called
        expect(onSubmitCalled, isFalse);
      });

      testWidgets('should handle async submission errors gracefully', (
        tester,
      ) async {
        // Mock onSubmit that throws an error
        Future<void> failingOnSubmit(Map<String, dynamic> data) async {
          throw Exception('Submission failed');
        }

        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: failingOnSubmit,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Fill form and submit
        await tester.enterText(find.byType(TextFormField).first, 'Valid Name');
        await tester.tap(find.text('Create'));
        await tester.pumpAndSettle();

        // Form should still be rendered (error handling should prevent crash)
        expect(find.text('Test Form'), findsOneWidget);
      });
    });

    group('Delete Functionality', () {
      testWidgets('should call onDelete when delete button is pressed', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
              showDeleteButton: true,
              onDelete: mockOnDelete,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Tap delete button
        await tester.tap(find.byIcon(Icons.delete_outline));
        await tester.pumpAndSettle();

        // Confirm delete in dialog
        await tester.tap(find.text('Delete'));
        await tester.pumpAndSettle();

        // Verify onDelete was called
        expect(onDeleteCalled, isTrue);
      });

      testWidgets('should not call onDelete when delete button is not shown', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
              showDeleteButton: false,
              onDelete: mockOnDelete,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Delete button should not be present
        expect(find.byIcon(Icons.delete_outline), findsNothing);
        expect(onDeleteCalled, isFalse);
      });
    });

    group('Loading States', () {
      testWidgets('should show loading indicator when isLoading is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
              isLoading: true,
            ),
          ),
        );
        // Use pump() instead of pumpAndSettle() to avoid infinite animation timeout
        await tester.pump();

        // Should show loading indicator
        expect(find.byType(LinearProgressIndicator), findsOneWidget);
      });

      testWidgets('should disable submit button when isLoading is true', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
              isLoading: true,
            ),
          ),
        );
        // Use pump() instead of pumpAndSettle() to avoid infinite animation timeout
        await tester.pump();

        // Submit button should be disabled (in create mode, button says "Create")
        final submitButton = find.text('Create');
        expect(submitButton, findsOneWidget);

        // Try to tap the button - should not trigger submission
        await tester.tap(submitButton);
        await tester.pump();

        expect(onSubmitCalled, isFalse);
      });
    });

    group('Custom Actions', () {
      testWidgets('should render custom actions when provided', (tester) async {
        final customActions = [
          TextButton(onPressed: () {}, child: const Text('Custom Action')),
        ];

        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
              customActions: customActions,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Should render custom action
        expect(find.text('Custom Action'), findsOneWidget);
      });

      testWidgets('should not render custom actions when not provided', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            BaseEditableFormScreen<String>(
              title: 'Test Form',
              fieldConfigs: testFieldConfigs,
              onSubmit: mockOnSubmit,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Should not render custom actions
        expect(find.text('Custom Action'), findsNothing);
      });
    });
  });
}
