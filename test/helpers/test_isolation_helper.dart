import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

import 'mock_providers.dart';

/// Helper class for ensuring proper test isolation and resource cleanup
///
/// This class provides utilities to prevent test timeouts and resource contention
/// by ensuring proper cleanup and isolation between tests.
class TestIsolationHelper {
  static ProviderContainer? _currentContainer;
  static final List<StreamSubscription<dynamic>> _activeSubscriptions = [];
  static final List<Timer> _activeTimers = [];

  /// Setup test isolation with fresh mocks and clean state
  static void setUp() {
    // Dispose previous container if exists
    cleanUp();

    // Reset all mocks to clean state
    MockProviders.resetMocks();
    MockProviders.setupDefaultMocks();
  }

  /// Clean up all resources after test completion
  static void tearDown() {
    cleanUp();
  }

  /// Create an isolated ProviderContainer for testing
  static ProviderContainer createIsolatedContainer({
    List<Override>? overrides,
  }) {
    // Dispose previous container
    _disposeCurrentContainer();

    // Create new container with fresh overrides
    final freshOverrides = <Override>[
      // Add fresh mock overrides here as needed
      ...?overrides,
    ];

    _currentContainer = ProviderContainer(overrides: freshOverrides);
    return _currentContainer!;
  }

  /// Register a stream subscription for automatic cleanup
  static T trackSubscription<T extends StreamSubscription<dynamic>>(
    T subscription,
  ) {
    _activeSubscriptions.add(subscription);
    return subscription;
  }

  /// Register a timer for automatic cleanup
  static Timer trackTimer(Timer timer) {
    _activeTimers.add(timer);
    return timer;
  }

  /// Perform comprehensive cleanup of all resources
  static void cleanUp() {
    // Cancel all active subscriptions
    for (final subscription in _activeSubscriptions) {
      try {
        subscription.cancel();
      } on Exception catch (_) {
        // Ignore cleanup errors
      }
    }
    _activeSubscriptions.clear();

    // Cancel all active timers
    for (final timer in _activeTimers) {
      try {
        timer.cancel();
      } on Exception catch (_) {
        // Ignore cleanup errors
      }
    }
    _activeTimers.clear();

    // Dispose current container
    _disposeCurrentContainer();

    // Reset mocks
    MockProviders.resetMocks();
  }

  /// Safely dispose the current ProviderContainer
  static void _disposeCurrentContainer() {
    if (_currentContainer != null) {
      try {
        _currentContainer!.dispose();
      } on Exception catch (_) {
        // Ignore disposal errors
      }
      _currentContainer = null;
    }
  }

  /// Pump widget with timeout protection to prevent infinite waits
  static Future<void> pumpWidgetSafely(
    WidgetTester tester,
    Widget widget, {
    Duration timeout = const Duration(seconds: 3),
  }) async {
    await tester.pumpWidget(widget);

    try {
      await tester.pumpAndSettle(timeout);
    } on Exception catch (e) {
      if (e.toString().contains('pumpAndSettle timed out')) {
        // If pumpAndSettle times out, just pump once more and continue
        await tester.pump();
        // ignore: avoid_print - Debug information for test timeouts
        print('Warning: pumpAndSettle timed out, continuing with single pump');
      } else {
        rethrow;
      }
    }
  }

  /// Wait for condition with timeout to prevent infinite waits
  static Future<bool> waitForCondition(
    WidgetTester tester,
    bool Function() condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    final stopwatch = Stopwatch()..start();

    while (!condition() && stopwatch.elapsed < timeout) {
      await tester.pump(interval);
    }

    return condition();
  }

  /// Verify that all resources are properly cleaned up
  static bool verifyCleanState() {
    return _activeSubscriptions.isEmpty &&
        _activeTimers.isEmpty &&
        _currentContainer == null;
  }
}

/// Mixin for test classes that need proper isolation and cleanup
mixin TestIsolationMixin {
  void setUpIsolation() {
    TestIsolationHelper.setUp();
  }

  void tearDownIsolation() {
    TestIsolationHelper.tearDown();
  }

  ProviderContainer createIsolatedContainer({List<Override>? overrides}) {
    return TestIsolationHelper.createIsolatedContainer(overrides: overrides);
  }

  Future<void> pumpWidgetSafely(
    WidgetTester tester,
    Widget widget, {
    Duration timeout = const Duration(seconds: 3),
  }) {
    return TestIsolationHelper.pumpWidgetSafely(
      tester,
      widget,
      timeout: timeout,
    );
  }
}
