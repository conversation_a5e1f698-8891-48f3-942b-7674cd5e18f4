import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

/// Helper class for Firebase testing setup and teardown
// ignore: avoid_classes_with_only_static_members
class FirebaseTestHelper {
  static bool _initialized = false;

  /// Initialize Firebase for testing
  ///
  /// This sets up Firebase with test configuration and connects to the
  /// Firebase Emulator Suite if available.
  static Future<void> initializeFirebase() async {
    if (_initialized) return;

    try {
      // Use demo project ID for emulator testing as recommended by Firebase docs
      await Firebase.initializeApp(
        options: const FirebaseOptions(
          apiKey: 'demo-api-key',
          appId: 'demo-app-id',
          messagingSenderId: 'demo-sender-id',
          projectId: 'demo-budapp-test',
        ),
      );

      // Connect to Firebase Emulator Suite if running
      await _connectToEmulators();

      _initialized = true;
      debugPrint('Firebase initialized for testing with demo project');
    } catch (e) {
      // Firebase might already be initialized
      if (e.toString().contains('already exists') ||
          e.toString().contains('duplicate-app')) {
        _initialized = true;
        await _connectToEmulators();
        debugPrint('Firebase already initialized, connected to emulators');
      } else {
        debugPrint('Firebase initialization error: $e');
        rethrow;
      }
    }
  }

  /// Connect to Firebase Emulator Suite
  static Future<void> _connectToEmulators() async {
    try {
      // Connect to Firestore Emulator (default port 8080)
      FirebaseFirestore.instance.useFirestoreEmulator('localhost', 8080);
      debugPrint('Connected to Firestore emulator on localhost:8080');

      // Connect to Auth Emulator (default port 9099)
      await FirebaseAuth.instance.useAuthEmulator('localhost', 9099);
      debugPrint('Connected to Auth emulator on localhost:9099');
    } on Exception catch (e) {
      // Emulators might already be connected or not running
      // This is acceptable for testing, but log the issue
      debugPrint('Emulator connection warning: $e');
    }
  }

  /// Clean up Firebase resources after testing
  static Future<void> cleanup() async {
    try {
      // Clear Firestore data if using emulator
      if (_isUsingEmulator()) {
        await _clearFirestoreData();
      }

      // Sign out any authenticated users
      if (FirebaseAuth.instance.currentUser != null) {
        await FirebaseAuth.instance.signOut();
      }
    } on Exception {
      // Cleanup errors are not critical for tests
    }
  }

  /// Check if we're using the Firebase Emulator
  static bool _isUsingEmulator() {
    try {
      // This is a simple check - in a real implementation you might
      // want to check environment variables or configuration
      return true; // Assume we're always using emulator in tests
    } on Exception {
      return false;
    }
  }

  /// Clear all Firestore data (emulator only)
  static Future<void> _clearFirestoreData() async {
    try {
      // In a real implementation, you would call the emulator's clear endpoint
      // For now, we'll just ensure we're not accidentally clearing production data
      if (!_isUsingEmulator()) {
        throw Exception('Cannot clear data when not using emulator');
      }

      // This is a placeholder - actual implementation would use HTTP requests
      // to the emulator's REST API to clear data
    } on Exception {
      // Clearing data is not critical for individual tests
    }
  }

  /// Create a test user with the given email and password
  static Future<User> createTestUser({
    required String email,
    required String password,
  }) async {
    final userCredential = await FirebaseAuth.instance
        .createUserWithEmailAndPassword(email: email, password: password);
    return userCredential.user!;
  }

  /// Delete a test user
  static Future<void> deleteTestUser(User user) async {
    try {
      await user.delete();
    } on Exception {
      // User might already be deleted or not exist
    }
  }

  /// Generate a unique test email
  static String generateTestEmail() {
    return 'test${DateTime.now().millisecondsSinceEpoch}@example.com';
  }

  /// Generate a unique test ID
  static String generateTestId([String? prefix]) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return prefix != null ? '${prefix}_$timestamp' : 'test_$timestamp';
  }
}
