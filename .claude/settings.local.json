{"permissions": {"allow": ["mcp__mcp-memory-bank__get_memory_bank_structure", "mcp__mcp-memory-bank__analyze_project_summary", "mcp__task-master-ai__get_task", "mcp__task-master-ai__set_task_status", "Bash(npm test)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(flutter gen-l10n:*)", "Bash(flutter analyze:*)", "<PERSON><PERSON>(flutter test:*)", "<PERSON><PERSON>(npx jest:*)", "Bash(grep:*)", "Bash(flutter pub:*)", "Bash(dart format:*)", "Bash(flutter packages:*)", "Bash(rg:*)", "<PERSON><PERSON>(dart run build_runner:*)", "mcp__task-master-ai__get_tasks", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__sequential-thinking__sequentialthinking", "mcp__task-master-ai__update_task", "Bash(find:*)", "mcp__task-master-ai__update_subtask", "WebFetch(domain:firebase.google.com)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(timeout 30 flutter test --no-coverage)", "Bash(flutter build:*)", "Bash(firebase database:settings:set:*)", "<PERSON><PERSON>(sed:*)", "Bash(for:*)", "Bash(do)", "Bash(done)", "Ba<PERSON>(flutter:*)", "<PERSON><PERSON>(timeout 30 flutter test:*)", "<PERSON><PERSON>(timeout 10 flutter test:*)", "Bash(timeout 60 flutter test --reporter=compact)", "mcp__task-master-ai__add_task", "mcp__task-master-ai__add_subtask", "Bash(ls:*)", "mcp__task-master-ai__add_dependency", "<PERSON>sh(git check-ignore:*)", "Bash(npm test:*)", "<PERSON><PERSON>(timeout 30 npm test)", "<PERSON><PERSON>(dart fix:*)", "Bash(genhtml:*)", "<PERSON><PERSON>(lcov:*)", "mcp__Ref__ref_search_documentation", "WebFetch(domain:docs.codacy.com)", "mcp__codacy__codacy_get_repository_with_analysis"], "deny": []}}